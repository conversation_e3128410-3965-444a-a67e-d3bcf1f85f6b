/*
 * ESP32-S3 Color Measurement System with Split Database and Loop Prevention
 * Fixed version to prevent infinite background color matching loops
 */

#include <WiFi.h>
#include <WebServer.h>
#include <LittleFS.h>
#include <ArduinoJson.h>
#include <Preferences.h>
#include <Wire.h>
#include <Adafruit_NeoPixel.h>
#include "DFRobot_TCS3430.h"

// Hardware Configuration
#define RGB_LED_PIN 18
#define RGB_LED_COUNT 1
#define BUZZER_PIN 42
#define TCS3430_INT_PIN 39

// WiFi Configuration
const char* ssid = "Wifi 6";
const char* password = "Scrofani1985";

// Web Server
WebServer server(80);

// RGB LED
Adafruit_NeoPixel strip(RGB_LED_COUNT, RGB_LED_PIN, NEO_GRB + NEO_KHZ800);

// Color Sensor
DFRobot_TCS3430 tcs3430;

// Preferences for EEPROM storage
Preferences preferences;

// Color matching structures
struct DuluxColorMatch {
    bool isValid;
    char name[64];
    char code[16];
    char lrv[8];
    char id[16];
    uint8_t r, g, b;
    float deltaE;
};

struct ColorSample {
    bool isValid;
    int sampleNumber;
    uint8_t r, g, b;
    char hex[8];
    char duluxName[64];
    char duluxCode[16];
    char duluxLRV[8];
    char duluxID[16];
    uint8_t duluxR, duluxG, duluxB;
    float deltaE;
    uint16_t rawX, rawY, rawZ;
    float confidence;
    unsigned long timestamp;
};

// Sample storage
const int MAX_STORED_SAMPLES = 30;
ColorSample storedSamples[MAX_STORED_SAMPLES];
int totalSamples = 0;
int currentSampleIndex = 0;

// Split database configuration
const char* DATABASE_FILES[] = {
    "/dulux_part1.json",
    "/dulux_part2.json", 
    "/dulux_part3.json",
    "/dulux_part4.json"
};
const int NUM_DATABASE_FILES = 4;
const size_t MAX_FILE_BUFFER_SIZE = 130000;
bool colorDatabaseLoaded = false;

// Background color matching prevention
static bool isBackgroundMatchingActive = false;
static unsigned long lastBackgroundMatchingTime = 0;
const unsigned long BACKGROUND_MATCHING_INTERVAL = 30000; // 30 seconds

// Current measurement state
struct {
    bool dataReady;
    uint8_t measuredR, measuredG, measuredB;
    char hexColor[8];
    uint16_t rawX, rawY, rawZ;
    uint16_t ir1, ir2;
    bool isScanning;
    bool ledState;
    bool hasFrozenColor;
    uint8_t frozenR, frozenG, frozenB;
    char frozenHex[8];
    float frozenConfidence;
    DuluxColorMatch currentMatch;
} measurementState = {0};

// Function declarations
void setupWiFi();
void setupWebServer();
void setupSensor();
void setupFileSystem();
bool loadSplitColorDatabase();
DuluxColorMatch searchColorDatabaseFile(const char* filename, uint8_t targetR, uint8_t targetG, uint8_t targetB);
DuluxColorMatch findBestColorMatch(uint8_t targetR, uint8_t targetG, uint8_t targetB);
void performBackgroundColorMatching();
void loadSamplesFromEEPROM();
void saveSamplesToEEPROM();
float calculateDeltaE(uint8_t r1, uint8_t g1, uint8_t b1, uint8_t r2, uint8_t g2, uint8_t b2);
void handleRoot();
void handleFullData();
void handleGetSamples();
void handleDeleteSample();
void handleClearSamples();
void handleSaveSample();
void handleStartScan();
void handleStopScan();
void handleToggleLED();
void handleCancel();

void setup() {
    Serial.begin(115200);
    Serial.println("ESP32-S3 Color Measurement System Starting...");
    
    // Initialize hardware
    strip.begin();
    strip.show();
    
    pinMode(BUZZER_PIN, OUTPUT);
    
    // Initialize file system
    setupFileSystem();
    
    // Initialize WiFi
    setupWiFi();
    
    // Initialize sensor
    setupSensor();
    
    // Load color database
    if (loadSplitColorDatabase()) {
        Serial.println("Split color database system ready!");
        colorDatabaseLoaded = true;
    } else {
        Serial.println("Failed to load split database - color matching disabled");
        colorDatabaseLoaded = false;
    }
    
    // Load saved samples
    loadSamplesFromEEPROM();
    
    // Setup web server
    setupWebServer();
    
    Serial.println("System initialization complete!");
}

void loop() {
    server.handleClient();
    
    // Handle sensor readings if scanning
    if (measurementState.isScanning) {
        // Sensor reading logic would go here
        // For now, just toggle LED to show activity
        measurementState.ledState = !measurementState.ledState;
        if (measurementState.ledState) {
            strip.setPixelColor(0, strip.Color(50, 0, 50));
        } else {
            strip.setPixelColor(0, strip.Color(0, 0, 0));
        }
        strip.show();
    }
    
    delay(10);
}

void setupWiFi() {
    WiFi.begin(ssid, password);
    Serial.print("Connecting to WiFi");
    
    while (WiFi.status() != WL_CONNECTED) {
        delay(500);
        Serial.print(".");
    }
    
    Serial.println();
    Serial.print("Connected! IP address: ");
    Serial.println(WiFi.localIP());
}

void setupFileSystem() {
    if (!LittleFS.begin(true)) {
        Serial.println("LittleFS Mount Failed");
        return;
    }
    Serial.println("LittleFS mounted successfully");
}

void setupSensor() {
    Wire.begin();

    if (tcs3430.begin()) {
        Serial.println("TCS3430 sensor initialized");
        // Configure sensor settings
        tcs3430.setIntegrationTime(64);  // ~181ms
        tcs3430.setALSGain(1);          // 4x gain
    } else {
        Serial.println("TCS3430 sensor not found - demo mode");
    }
}

// Check if split database files exist and are accessible
bool loadSplitColorDatabase() {
    Serial.println("Checking split color database files...");

    bool allFilesExist = true;
    size_t totalSize = 0;

    for (int i = 0; i < NUM_DATABASE_FILES; i++) {
        if (!LittleFS.exists(DATABASE_FILES[i])) {
            Serial.printf("ERROR: %s not found\n", DATABASE_FILES[i]);
            allFilesExist = false;
        } else {
            File file = LittleFS.open(DATABASE_FILES[i], "r");
            if (file) {
                size_t fileSize = file.size();
                totalSize += fileSize;
                Serial.printf("Found %s: %d bytes\n", DATABASE_FILES[i], fileSize);
                file.close();
            } else {
                Serial.printf("ERROR: Cannot open %s\n", DATABASE_FILES[i]);
                allFilesExist = false;
            }
        }
    }

    if (!allFilesExist) {
        Serial.println("ERROR: Not all split database files available");
        return false;
    }

    Serial.printf("All %d split database files found (%d bytes total)\n", NUM_DATABASE_FILES, totalSize);
    return true;
}

// Search through a single database file for color matches
DuluxColorMatch searchColorDatabaseFile(const char* filename, uint8_t targetR, uint8_t targetG, uint8_t targetB) {
    DuluxColorMatch bestMatch = {0};
    bestMatch.isValid = false;
    bestMatch.deltaE = 999.0f;
    strcpy(bestMatch.name, "No Match Found");
    strcpy(bestMatch.code, "N/A");
    strcpy(bestMatch.lrv, "N/A");
    strcpy(bestMatch.id, "N/A");
    bestMatch.r = targetR;
    bestMatch.g = targetG;
    bestMatch.b = targetB;

    File file = LittleFS.open(filename, "r");
    if (!file) {
        Serial.printf("Failed to open %s\n", filename);
        return bestMatch;
    }

    size_t fileSize = file.size();
    char* buffer = (char*)ps_malloc(MAX_FILE_BUFFER_SIZE);
    if (!buffer) {
        Serial.printf("Failed to allocate buffer for %s\n", filename);
        file.close();
        return bestMatch;
    }

    size_t bytesRead = file.readBytes(buffer, min(fileSize, MAX_FILE_BUFFER_SIZE - 1));
    buffer[bytesRead] = '\0';
    file.close();

    JsonDocument doc;
    DeserializationError error = deserializeJson(doc, buffer);

    if (error) {
        Serial.printf("JSON parsing failed for %s: %s\n", filename, error.c_str());
        free(buffer);
        return bestMatch;
    }

    JsonArray colors = doc.as<JsonArray>();
    int colorsProcessed = 0;

    for (JsonObject color : colors) {
        colorsProcessed++;

        int r = color["r"].as<int>();
        int g = color["g"].as<int>();
        int b = color["b"].as<int>();

        float deltaE = calculateDeltaE(targetR, targetG, targetB, r, g, b);

        if (deltaE < bestMatch.deltaE) {
            bestMatch.deltaE = deltaE;
            bestMatch.isValid = true;
            bestMatch.r = r;
            bestMatch.g = g;
            bestMatch.b = b;

            strncpy(bestMatch.name, color["name"] | "Unknown", sizeof(bestMatch.name) - 1);
            bestMatch.name[sizeof(bestMatch.name) - 1] = '\0';

            strncpy(bestMatch.code, color["code"] | "N/A", sizeof(bestMatch.code) - 1);
            bestMatch.code[sizeof(bestMatch.code) - 1] = '\0';

            strncpy(bestMatch.lrv, color["lrv"] | "N/A", sizeof(bestMatch.lrv) - 1);
            bestMatch.lrv[sizeof(bestMatch.lrv) - 1] = '\0';

            strncpy(bestMatch.id, color["id"] | "N/A", sizeof(bestMatch.id) - 1);
            bestMatch.id[sizeof(bestMatch.id) - 1] = '\0';

            if (deltaE < 1.0f) {
                Serial.printf("Excellent match found: %s (ΔE=%.1f)\n", bestMatch.name, deltaE);
                break;
            }
        }

        if (colorsProcessed % 100 == 0) {
            yield();
        }
    }

    Serial.printf("Processed %d colors from %s, best ΔE: %.1f\n", colorsProcessed, filename, bestMatch.deltaE);
    free(buffer);
    return bestMatch;
}
