/*
 * ESP32-S3 Color Matching Device - Working Version with WiFi and Loop Prevention
 *
 * FIXED VERSION to prevent infinite background color matching loops
 *
 * Features:
 * - TCS3430 color sensor reading with I2C communication
 * - WiFi connectivity using ESP32 built-in WiFi
 * - Split database color matching with memory optimization
 * - LED status indicators with RGB LED
 * - Web interface for color measurement and sample management
 * - Background color matching with loop prevention
 * - EEPROM sample storage with 30-sample circular buffer
 *
 * Hardware Configuration:
 * - I2C: SDA=8, SCL=9
 * - RGB LED: GPIO 18
 * - Buzzer: GPIO 42
 * - Sensor Interrupt: GPIO 39
 *
 * Network Configuration:
 * - SSID: "Wifi 6"
 * - Password: "Scrofani1985"
 * - Static IP: *************
 */

#include <Arduino.h>
#include <Wire.h>
#include <WiFi.h>
#include "DFRobot_TCS3430.h"  // Use local library
#include <ArduinoJson.h>
#include <math.h>
#include <WebServer.h>
#include <LittleFS.h>
#include <Adafruit_NeoPixel.h>
#include <Preferences.h>

// Memory management for efficient color database storage
#include "esp_system.h"
#include "esp_heap_caps.h"

// Hardware Configuration for ESP32-S3 ProS3
#define I2C_SDA_PIN 8
#define I2C_SCL_PIN 9
#define RGB_LED_PIN 18
#define BUZZER_PIN 42
#define TCS3430_INT_PIN 39

// Network Configuration
const char* ssid = "Wifi 6";
const char* password = "Scrofani1985";
IPAddress local_IP(192, 168, 0, 201);
IPAddress gateway(192, 168, 0, 1);
IPAddress subnet(255, 255, 255, 0);

// Hardware objects
WebServer server(80);
Adafruit_NeoPixel strip(1, RGB_LED_PIN, NEO_GRB + NEO_KHZ800);
DFRobot_TCS3430 tcs3430;
Preferences preferences;

// LVGL Display flush callback function
void display_flush_callback(lv_disp_drv_t *disp_drv, const lv_area_t *area, lv_color_t *color_p) {
    if (gfx) {
        uint32_t w = lv_area_get_width(area);
        uint32_t h = lv_area_get_height(area);
        gfx->draw16bitRGBBitmap(area->x1, area->y1, (uint16_t *)color_p, w, h);
    }
    lv_disp_flush_ready(disp_drv); // Inform LVGL that flushing is complete
}

// LVGL Touchpad read callback function (basic I2C implementation for CST816)
void touchpad_read_callback(lv_indev_drv_t *indev_drv, lv_indev_data_t *data) {
    static lv_coord_t last_x = 0;
    static lv_coord_t last_y = 0;
    static bool touch_controller_available = true;
    static uint32_t last_error_time = 0;
    bool touched = false;

    // Skip touch reading if controller is not available (to prevent blocking)
    if (!touch_controller_available && (millis() - last_error_time < 5000)) {
        data->point.x = last_x;
        data->point.y = last_y;
        data->state = LV_INDEV_STATE_REL; // Always released if controller not available
        return;
    }

    Wire.beginTransmission(TOUCH_I2C_ADDRESS);
    Wire.write(0x01); // Start reading from register 0x01 (Gesture / Touch Status)
    if (Wire.endTransmission(false) == 0) { // Send restart, keep connection active
        if (Wire.requestFrom((uint8_t)TOUCH_I2C_ADDRESS, (uint8_t)6) == 6) { // Request 6 bytes
            touch_controller_available = true; // Controller is responding

            // Byte 0: Gesture (not used here)
            // Byte 1: Touch points (0 or 1 for single touch)
            // Byte 2: X high bits
            // Byte 3: X low bits
            // Byte 4: Y high bits
            // Byte 5: Y low bits
            Wire.read(); // Skip gesture byte
            uint8_t touch_points = Wire.read();
            if (touch_points > 0) {
                uint8_t x_h = Wire.read();
                uint8_t x_l = Wire.read();
                uint8_t y_h = Wire.read();
                uint8_t y_l = Wire.read();

                last_x = ((x_h & 0x0F) << 8) | x_l; // Extract X coordinate
                last_y = ((y_h & 0x0F) << 8) | y_l; // Extract Y coordinate

                if (last_x < TFT_WIDTH && last_y < TFT_HEIGHT) { // Check bounds
                    touched = true;
                }
            }
        } else {
            // Failed to read - mark controller as unavailable
            touch_controller_available = false;
            last_error_time = millis();
        }
    } else {
        // Failed to communicate - mark controller as unavailable
        touch_controller_available = false;
        last_error_time = millis();
    }

    data->point.x = last_x;
    data->point.y = last_y;
    data->state = touched ? LV_INDEV_STATE_PR : LV_INDEV_STATE_REL; // Set state (Pressed/Released)
}

// Sensor object instance
DFRobot_TCS3430 tcs3430_sensor;

// Sensor availability flag
bool sensor_available = false;

// Interrupt flag for sensor
volatile bool color_change_interrupt_flag = false;

// Interrupt Service Routine (ISR) for sensor interrupt pin
void IRAM_ATTR handle_sensor_interrupt() {
  color_change_interrupt_flag = true; // Set flag, keep ISR short
}

uint32_t last_sensor_read_time_ms = 0; // Tracks time of last sensor read

void setup() {
    Serial.begin(115200);
    // while (!Serial && millis() < 2000); // Optional: wait for serial connection
    Serial.println("Booting up Color Matcher application...");

    // Initialize I2C communication
    Wire.begin(I2C_SDA_PIN, I2C_SCL_PIN);
    Serial.println("I2C interface initialized.");

    // Initialize board-specific peripherals
    init_rgb_led(); // Initialize onboard RGB LED (if any)
    init_buzzer();  // Initialize onboard buzzer
    init_power_control(); // Initialize power management

    set_rgb_led_color(10, 0, 10); // Purple boot indicator
    buzzer_beep(1000, 100); // Short boot beep

    // Initialize Display (using Arduino_GFX_Library)
    //log_info("Display", "Initializing display with pins: MOSI=11, SCK=10, CS=8, DC=2, RST=12, BL=16");

    // Explicitly set backlight pin before display init
    pinMode(TFT_BL_PIN, OUTPUT);
    digitalWrite(TFT_BL_PIN, HIGH); // Turn on backlight early
    //log_info("Display", "Backlight pin set HIGH before display init.");

    #if defined(TFT_SPI_HOST) // SPI display
        // Use 40MHz SPI frequency as in minimal test sketch
        bus = new Arduino_ESP32SPI(TFT_DC_PIN, TFT_CS_PIN, TFT_SCLK_PIN, TFT_MOSI_PIN, -1 /* MISO */, TFT_SPI_HOST, 40000000);
    #else // Add other bus types like 8-bit parallel if your board uses it
        //log_error("Display", "No valid display bus type defined for GFX!");
        while(1); // Halt
    #endif

    // ST7789V2 display controller - optimized parameters for ESP32-S3-Touch-LCD-1.69
    // Parameters: bus, reset_pin, rotation, IPS, width, height, col_offset1, row_offset1, col_offset2, row_offset2
    // Changed offsets to 0,20,0,20 for alignment testing
    // Adjusted offsets to 0,0,0,0 to fix display blank issue
    gfx = new Arduino_ST7789(bus, TFT_RST_PIN, 0 /* rotation */, true /* IPS panel */, TFT_WIDTH, TFT_HEIGHT, 0, 0, 0, 0);

    Serial.println("Starting display initialization...");

    // Manual reset sequence for better reliability
    pinMode(TFT_RST_PIN, OUTPUT);
    digitalWrite(TFT_RST_PIN, LOW);
    Serial.println("TFT_RST_PIN set LOW");
    delay(20);
    digitalWrite(TFT_RST_PIN, HIGH);
    Serial.println("TFT_RST_PIN set HIGH");
    delay(150);

    if (!gfx->begin()) {
        Serial.println("ERROR: gfx->begin() failed! Check display connections.");
        Serial.println("Pins: MOSI, SCK, CS, DC, RST");
        buzzer_beep(500, 1000); // Long low beep for error
    } else {
        Serial.println("Display begin() successful!");

        Serial.println("Drawing test patterns...");
        gfx->fillScreen(RED);     // Red screen
        Serial.println("Red screen drawn");
        delay(1000);
        gfx->fillScreen(GREEN);   // Green screen
        Serial.println("Green screen drawn");
        delay(1000);
        gfx->fillScreen(BLUE);    // Blue screen
        Serial.println("Blue screen drawn");
        delay(1000);
        gfx->fillScreen(WHITE);   // White screen
        Serial.println("White screen drawn");
        delay(1000);
        gfx->fillScreen(BLACK);   // Black screen
        Serial.println("Black screen drawn");
        delay(500);

        // Draw some text to verify display is working
        gfx->setTextColor(WHITE);
        gfx->setTextSize(2);
        gfx->setCursor(10, 50);
        gfx->println("ESP32-S3");
        Serial.println("Printed 'ESP32-S3'");
        gfx->setCursor(10, 80);
        gfx->println("Display");
        Serial.println("Printed 'Display'");
        gfx->setCursor(10, 110);
        gfx->println("Working!");
        Serial.println("Printed 'Working!'");

        Serial.println("Test patterns and text complete. GFX display driver initialized.");
    }

    #ifdef GFX_BL // If backlight pin is defined
        // Remove redundant backlight control here since handled earlier
    #else
        //log_warning("Display", "No backlight pin defined!");
    #endif

    Serial.println("Initializing LVGL library...");
    lv_init();
    Serial.println("LVGL initialized.");

    // Initialize LVGL display buffer
    lv_disp_draw_buf_init(&lvgl_disp_buf, lvgl_buf_1, lvgl_buf_2, LV_DISP_BUFFER_SIZE);
    Serial.println("LVGL display buffer initialized.");

    // Initialize LVGL display driver
    static lv_disp_drv_t lvgl_disp_drv;
    lv_disp_drv_init(&lvgl_disp_drv);
    lvgl_disp_drv.hor_res = TFT_WIDTH;
    lvgl_disp_drv.ver_res = TFT_HEIGHT;
    lvgl_disp_drv.flush_cb = display_flush_callback;
    lvgl_disp_drv.draw_buf = &lvgl_disp_buf;
    if (!lv_disp_drv_register(&lvgl_disp_drv)) {
        Serial.println("ERROR: LVGL display driver registration failed!");
        while(1); // Halt
    } else {
        Serial.println("LVGL display driver registered successfully.");
    }

    // Initialize LVGL Input Device (Touchpad) - CST816T Touch Controller
    static lv_indev_drv_t lvgl_indev_drv;
    lv_indev_drv_init(&lvgl_indev_drv);
    lvgl_indev_drv.type = LV_INDEV_TYPE_POINTER; // Touchpad is a pointer type input
    lvgl_indev_drv.read_cb = touchpad_read_callback;
    if (!lv_indev_drv_register(&lvgl_indev_drv)) {
        Serial.println("WARNING: LVGL input device (touchpad) registration failed!");
        // Can continue without touch, but UI will be non-interactive
    } else {
        Serial.println("LVGL CST816T touch input enabled and registered.");
    }
    Serial.println("LVGL core and drivers initialized.");

    // Initialize our custom User Interface elements
    ui_init();
    Serial.println("Custom UI initialized.");

    // Initialize Calibration System (loads defaults or from NVS)
    calibration_init();
    Serial.println("Calibration system initialized.");

    // Initialize Color Database (pre-calculates Lab values)
    initialize_color_database();
    Serial.println("Reference color database initialized.");

    // Initialize TCS3430 Color Sensor using DFRobot Library
    Serial.println("Attempting to initialize TCS3430 color sensor...");
    int sensor_retry_count = 0;
    const int MAX_SENSOR_RETRIES = 3;

    while (!tcs3430_sensor.begin() && sensor_retry_count < MAX_SENSOR_RETRIES) {
        sensor_retry_count++;
        Serial.printf("TCS3430 sensor not detected (attempt %d/%d). Check I2C connection.\n", sensor_retry_count, MAX_SENSOR_RETRIES);
        delay(1000); // Shorter delay between retries
    }

    if (sensor_retry_count >= MAX_SENSOR_RETRIES) {
        Serial.println("WARNING: TCS3430 sensor not found. Application will continue without sensor.");
        show_ui_message("Sensor not connected - Demo mode", false);
        sensor_available = false;
    } else {
        Serial.println("TCS3430 color sensor detected and initialized.");
        show_ui_message("Sensor ready", false);
        sensor_available = true;
    }

    // Configure TCS3430 sensor parameters (based on V1.7 sketch insights)
    tcs3430_sensor.setIntegrationTime(0x40); // 65 cycles, ~181ms integration time for good resolution
    tcs3430_sensor.setALSGain(1);            // 4x gain (options: 0=1x, 1=4x, 2=16x, 3=64x) - adjust for brightness
    tcs3430_sensor.setALSInterrupt(true);    // Enable ALS interrupt
    tcs3430_sensor.setInterruptPersistence(0x05); // Interrupt after 10 consecutive out-of-threshold readings
    // Note: setCH0IntThreshold refers to X/Clear channel. Adjust thresholds based on expected light levels for this channel.
    tcs3430_sensor.setCH0IntThreshold(/*low*/1000, /*high*/50000); 
    tcs3430_sensor.setIntReadClear(true);    // Interrupt flag clears on status read
    tcs3430_sensor.setSleepAfterInterrupt(true); // Sensor sleeps after interrupt to save power
    // Optional advanced settings from V1.7:
    // tcs3430_sensor.setAutoZeroMode(1); 
    // tcs3430_sensor.setAutoZeroNTHIteration(7);

    // Setup sensor interrupt pin
    if (SENSOR_INTERRUPT_PIN >= 0) {
        pinMode(SENSOR_INTERRUPT_PIN, INPUT_PULLUP); // Use pullup for interrupt pin
        attachInterrupt(digitalPinToInterrupt(SENSOR_INTERRUPT_PIN), handle_sensor_interrupt, FALLING);
        Serial.println("Interrupt configured on pin 17 (GPIO17).");
    } else {
        Serial.println("WARNING: SENSOR_INTERRUPT_PIN not defined. Interrupts disabled.");
    }
    
    set_rgb_led_color(0, 0, 0); // Turn off boot indicator LED
    buzzer_beep(800, 200); // Success beep (higher pitch, longer duration)
    Serial.println("Setup complete. Entering main loop.");
    show_ui_message("Ready to measure!", false); // Initial message on UI
}

void loop() {
    uint32_t current_time_ms = millis();

    // Let LVGL handle its tasks (animations, input processing, redrawing)
    lv_timer_handler();

    // Check if sensor interrupt flag was set
    if (color_change_interrupt_flag) {
        color_change_interrupt_flag = false; // Reset flag
        tcs3430_sensor.getDeviceStatus();    // Clear interrupt status on the sensor itself
        log_info("Sensor", "Interrupt triggered! Significant color change detected.");
        show_ui_message("Color change detected!", false);
        // Force an immediate sensor read by making it seem like the interval has passed
        last_sensor_read_time_ms = current_time_ms - SENSOR_READ_INTERVAL_MS; 
    }

    // Perform sensor reading and UI update at defined intervals (only if sensor is available)
    if (sensor_available && (current_time_ms - last_sensor_read_time_ms >= SENSOR_READ_INTERVAL_MS)) {
        last_sensor_read_time_ms = current_time_ms;
        // clear_ui_message(); // Clear previous non-error message before new reading cycle

        // Read raw data from TCS3430 sensor
        uint16_t x_val_raw = tcs3430_sensor.getXData();
        uint16_t y_val_raw = tcs3430_sensor.getYData();
        uint16_t z_val_raw = tcs3430_sensor.getZData();
        uint16_t ir1_val_raw = tcs3430_sensor.getIR1Data();
        uint16_t ir2_val_raw = tcs3430_sensor.getIR2Data(); // Note: this call has an internal delay

        // Store raw sensor values (as floats for processing)
        xyz_color_s measured_xyz_from_sensor = {(float)x_val_raw, (float)y_val_raw, (float)z_val_raw};

        // Apply IR compensation using the calibrated factor
        apply_ir_compensation(measured_xyz_from_sensor, ir1_val_raw, ir2_val_raw, current_app_calibration.k_ir_compensation_factor);

        // Convert compensated sensor XYZ to L*a*b* for color matching
        // This uses D65_WHITEPOINT for normalization within xyz_to_lab
        lab_color_s measured_lab_color;
        xyz_to_lab(measured_xyz_from_sensor, measured_lab_color, D65_WHITEPOINT);

        // Find the closest color in our reference database
        float delta_e_value = -1.0f; // Initialize to indicate no match / error
        int matched_color_index = find_closest_color_in_db(measured_lab_color, delta_e_value);
        const char* matched_color_name_str = (matched_color_index != -1) ? dulux_color_database[matched_color_index].name : "Unknown";

        // Convert compensated sensor XYZ to sRGB for display on the swatch
        // Uses calibrated normalization factor for display accuracy.
        rgb_color_s display_rgb_color = xyz_to_display_srgb(measured_xyz_from_sensor, current_app_calibration.display_srgb_norm_factor);
        
        // Update the UI with all new data
        update_color_display(display_rgb_color, measured_xyz_from_sensor, ir1_val_raw, ir2_val_raw, matched_color_name_str, delta_e_value);

        // Optional: Set onboard RGB LED and audio feedback based on match or measured color
        if (matched_color_index != -1 && strcmp(matched_color_name_str, "Vivid White") == 0 && delta_e_value <= DELTA_E_TOLERANCE) {
            log_info("Match", "Vivid White™ detected on UI!");
            // show_ui_message("MATCH: Vivid White!", false); // Can be repetitive, consider only on state change
            set_rgb_led_color(255,255,255); // Bright white LED for Vivid White match
            buzzer_beep(1200, 150); // High-pitched success beep for exact match
        } else if (matched_color_index != -1 && delta_e_value <= DELTA_E_TOLERANCE) {
            // Good match but not Vivid White
            set_rgb_led_color(display_rgb_color.r / 2, display_rgb_color.g / 2, display_rgb_color.b / 2);
            buzzer_beep(900, 100); // Medium-pitched beep for good match
        } else {
             // Set LED to a dimmer version of the measured color for general feedback
             set_rgb_led_color(display_rgb_color.r / 4, display_rgb_color.g / 4, display_rgb_color.b / 4);
        }
    }

    delay(5); // Small delay for stability and to allow other tasks (if any in FreeRTOS)
}